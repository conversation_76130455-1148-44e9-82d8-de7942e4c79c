{
    // Use IntelliSense to learn about possible Node.js debug attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [        
        {
            "type": "node",
            "request": "attach",
            "name": "Attach by Process ID",
            "processId": "${command:PickProcess}",
            "protocol": "inspector"
        },
        {
            "name": "Attach",
            "type": "node",
            "request": "attach",
            "port": 9229,
            "address": "localhost",
            "restart": true,
            "sourceMaps": true,
            "outFiles": ["${workspaceRoot}/src"],
            "skipFiles": [
                "${workspaceFolder}/app/node_modules/**/*.js"
            ],
            "localRoot": "${workspaceRoot}/src",
            "remoteRoot": "${workspaceRoot}/src",
            "smartStep": true,
            "trace": true
        },
    ]
}