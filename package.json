{"name": "express-ts-template", "version": "1.2.0", "description": "Simple starter template for ExpressJs and TypeScript based on expressjs/generator and TypeScript-Node-Starter", "main": "server.js", "scripts": {"start": "npm run serve", "build": "npm run build-ts", "serve": "node dist/server.js", "test": "jest --coverage=false --verbose", "watch": "concurrently -k -p \"[{name}]\" -n \"TypeScript,Node\" -c \"cyan.bold,green.bold\" \"npm run watch-ts\" \"npm run watch-node\"", "watch-node": "nodemon dist/server.js", "build-ts": "tsc", "watch-ts": "tsc -w", "lint": "eslint src/*.ts", "lint-fix": "yarn lint --fix", "debug": "npm run build && npm run watch-debug", "serve-debug": "nodemon --inspect dist/server.js", "watch-debug": "concurrently -k -p \"[{name}]\" -n \"TypeScript,Node\" -c \"cyan.bold,green.bold\" \"npm run watch-ts\" \"npm run serve-debug\"", "typecheck": "tsc --noEmit", "run-prettier": "prettier src/*.ts", "validate-prettiness": "yarn run-prettier -c", "make-prettier": "yarn run-prettier --write"}, "author": "", "license": "ISC", "dependencies": {"@computer-use/nut-js": "^4.2.0", "express": "^4.16.2", "http-errors": "^1.7.1", "morgan": "^1.9.1", "pug": "^3.0.1"}, "devDependencies": {"@types/express": "4.17.21", "@types/http-errors": "1.8.2", "@types/jest": "26.0.24", "@types/morgan": "1.9.9", "@types/node": "14.18.63", "@types/supertest": "2.0.16", "@typescript-eslint/eslint-plugin": "4.33.0", "@typescript-eslint/parser": "4.33.0", "concurrently": "6.5.1", "eslint": "7.32.0", "eslint-config-prettier": "8.10.0", "eslint-plugin-import": "2.31.0", "jest": "26.6.3", "nodemon": "2.0.22", "prettier": "2.8.8", "supertest": "6.3.3", "ts-jest": "26.5.6", "typescript": "4.9.5"}}