import { Request, Response } from "express";
import { executeAutomaFlowById } from '../helpers/executeflow';

/**
 * GET /
 * Home page.
 */
export const index = async (req: Request, res: Response): Promise<void> => {
    res.render("index", { title: "Express" });
};


// export const startFlow = async (req: Request, res: Response): Promise<void> => {
//     res.render("startFlow", { title: "Express" });
// };

export const completeFlow = async (req: Request, res: Response): Promise<void> => {
    // 获取 resultUrl、bizInfo 回传到后端（WebSocket）
};
