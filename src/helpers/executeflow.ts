import { exec } from 'child_process';

const system = process.platform === 'darwin' ? 'macos' : 'windows';

// Window User
function openUrl(url: string) {
  if (system === 'windows') {
    return exec(`explorer ${url}`);
  }
  return exec(`open ${url}`);
}

// automaExecution
export function executeAutomaFlowById(flowId: string) {
    return openUrl(`chrome-extension://infppggnoaenmfagbfknfkancpbljcca/execute.html#/${flowId}`);
}
